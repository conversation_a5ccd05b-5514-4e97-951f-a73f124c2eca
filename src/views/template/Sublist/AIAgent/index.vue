<template>
    <div class="ai-agent">
        <div class="ai-agent__switch">
            <el-switch
                v-model="enableAgent"
                active-color="#13ce66"
                :disabled="loading"
                @change="handleSwitchChange"
            ></el-switch>
            <span>拖拽Agent</span>
        </div>
        <div class="ai-agent__description">
            <p>凡是从本地上传的文档，Agent都会自动帮你找到各个签约方的盖章处，以代替人工施动盖章处的繁琐操作</p>
            <p>*单份文档页数不能超过50页</p>
            <p>*配合"模板专用章"、"自动盖"等功能，可以实现非标合同盖章的完全自动化</p>
        </div>

        <div class="ai-agent__config" v-loading="loading">
            <h3>拖章规则配置</h3>
            <p class="config-tip">勾选需要使用的规则，并调整各个规则的排序，以明确规则冲突时的优先级。</p>
            
            <div class="rule-item">
                <el-checkbox v-model="strategyConfig.needSignature" :disabled="!enableAgent">需要骑缝章</el-checkbox>
                <p class="rule-desc">*如果不勾选，则不会自动添加骑缝章盖章处</p>
            </div>

            <div class="rule-item">
                <el-checkbox v-model="strategyConfig.matchSignature" :disabled="!enableAgent">对等盖章：按已有盖章处匹配签约方的盖章处</el-checkbox>
                <p class="rule-desc">*例如，若合同上已有对方的3个印章（分布在不同页面），系统会自动为我方在相应位置也生成3个对应的盖章位置。 注：本功能仅在单方盖章时生效。</p>
            </div>

            <div class="rule-item">
                <el-checkbox v-model="strategyConfig.endSignature" :disabled="!enableAgent">文件中若没有明确的盖章处，则盖在文件最后一行的文字上</el-checkbox>
                <p class="rule-desc">*不是传统意义上的合同，没有明确的盖章处指示的文件，如内部单据等需要盖章的文件</p>
            </div>

            <div class="rule-item">
                <el-checkbox v-model="strategyConfig.placeSignature" :disabled="!enableAgent">印章应放置在合理的区域（如盖章栏附近、合同末页公司信息处等）</el-checkbox>
                <p class="rule-desc">*可能会与对等盖章、每页盖章等规则有冲突，需要调整优先级，以确保优先需要满足的规则</p>
            </div>

            <div class="rule-item">
                <el-checkbox v-model="strategyConfig.everyPageSignature" :disabled="!enableAgent">每页盖章</el-checkbox>
                <p class="rule-desc">*如果是对账单、招投标文件，则每页相同位置都需要盖章</p>
            </div>
        </div>

        <div class="ai-agent__adjustment">
            <h3>自助调优</h3>
            <div class="adjustment-item">
                <p>(1) 盖章位置调优（不含骑缝章）</p>
                <div class="position-adjust">
                    <div class="adjust-row">
                        <span>系统指定的盖章处需向上移动</span>
                        <el-input v-model="positionAdjust.upAdjust" size="small" class="adjust-input" :disabled="!enableAgent"></el-input>
                        <span>厘米</span>
                    </div>
                    <div class="adjust-row">
                        <span>系统指定的盖章处需向左移动</span>
                        <el-input v-model="positionAdjust.leftAdjust" size="small" class="adjust-input" :disabled="!enableAgent"></el-input>
                        <span>厘米</span>
                    </div>
                </div>
                <p class="adjust-tip">*可填写负数，代表反方向。</p>
                <p class="adjust-tip">*N厘米是打印后的大小，默认情况下一个章的直径约4厘米，可以以此折算</p>
                <p class="adjust-tip">*移动盖章处可能会与已有盖章处重叠，请知悉</p>
            </div>
            
            <div class="adjustment-item">
                <p>(2) 是否盖章调整</p>
                <div class="content-adjust">
                    <div class="adjust-row">
                        <span>若文件中出现</span>
                        <el-input v-model="keywordConfig.contentToSign" size="small" class="content-input" :disabled="!enableAgent"></el-input>
                        <span>该内容对应的位置需要盖章</span>
                    </div>
                    <div class="adjust-row">
                        <span>若文件中出现</span>
                        <el-input v-model="keywordConfig.contentNotToSign" size="small" class="content-input" :disabled="!enableAgent"></el-input>
                        <span>该内容对应的位置不需要盖章</span>
                    </div>
                </div>
                <p class="adjust-tip">*此处配置后，规则将变成最高优先级</p>
            </div>
        </div>

        <div class="ai-agent__actions">
            <el-button
                type="primary"
                :loading="loading"
                :disabled="!enableAgent"
                @click="saveConfig"
            >
                保存配置
            </el-button>
            <el-button @click="resetConfig">重置配置</el-button>
        </div>
    </div>
</template>

<script>
import { getStampRecommendationRule, saveStampRecommendationRule, toggleStampRecommendationSwitch } from 'src/api/template/index.js';

export default {
    name: 'AIAgent',
    props: {
        templateId: {
            type: String,
            required: true
        },
        templateName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            enableAgent: false,
            loading: false,
            // 策略配置映射
            strategyConfig: {
                needSignature: false,
                matchSignature: true,
                endSignature: true,
                placeSignature: true,
                everyPageSignature: false
            },
            // 位置调优配置
            positionAdjust: {
                upAdjust: '0',
                leftAdjust: '0'
            },
            // 关键字配置
            keywordConfig: {
                contentToSign: '',
                contentNotToSign: ''
            },
            // 策略类型映射
            strategyTypeMap: {
                needSignature: 'NEED_OVERLAPPING_SEAL',
                matchSignature: 'RECIPROCAL_SEALING',
                endSignature: 'SEAL_ON_LAST_LINE_OF_TEXT',
                placeSignature: 'REASONABLE_AREA_SEALING',
                everyPageSignature: 'SEAL_EACH_PAGE'
            }
        };
    },
    mounted() {
        this.loadConfig();
    },
    methods: {
        // 加载配置
        async loadConfig() {
            if (!this.templateId) return;

            this.loading = true;
            try {
                const response = await getStampRecommendationRule(this.templateId);
                const data = response.data;

                // 设置开关状态
                this.enableAgent = data.useStampRecommendation || false;

                // 解析策略配置
                if (data.stampStrategies && Array.isArray(data.stampStrategies)) {
                    this.parseStrategiesFromAPI(data.stampStrategies);
                }
            } catch (error) {
                console.error('加载印章推荐配置失败:', error);
                this.$message.error('加载配置失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        // 解析API返回的策略配置
        parseStrategiesFromAPI(strategies) {
            // 重置所有配置
            this.resetConfig();

            strategies.forEach(strategy => {
                const { strategyType, strategyConfigParam } = strategy;

                switch (strategyType) {
                    case 'NEED_OVERLAPPING_SEAL':
                        this.strategyConfig.needSignature = true;
                        break;
                    case 'RECIPROCAL_SEALING':
                        this.strategyConfig.matchSignature = true;
                        break;
                    case 'SEAL_ON_LAST_LINE_OF_TEXT':
                        this.strategyConfig.endSignature = true;
                        break;
                    case 'REASONABLE_AREA_SEALING':
                        this.strategyConfig.placeSignature = true;
                        break;
                    case 'SEAL_EACH_PAGE':
                        this.strategyConfig.everyPageSignature = true;
                        break;
                    case 'SEAL_MOVE_CONFIG':
                        if (strategyConfigParam) {
                            this.positionAdjust.upAdjust = strategyConfigParam.upMove || '0';
                            this.positionAdjust.leftAdjust = strategyConfigParam.leftMove || '0';
                        }
                        break;
                    case 'ADD_SEAL_BY_KEYWORD':
                        if (strategyConfigParam && strategyConfigParam.keyword) {
                            this.keywordConfig.contentToSign = strategyConfigParam.keyword;
                        }
                        break;
                    case 'REMOVE_SEAL_BY_KEYWORD':
                        if (strategyConfigParam && strategyConfigParam.keyword) {
                            this.keywordConfig.contentNotToSign = strategyConfigParam.keyword;
                        }
                        break;
                }
            });
        },

        // 重置配置
        resetConfig() {
            this.strategyConfig = {
                needSignature: false,
                matchSignature: false,
                endSignature: false,
                placeSignature: false,
                everyPageSignature: false
            };
            this.positionAdjust = {
                upAdjust: '0',
                leftAdjust: '0'
            };
            this.keywordConfig = {
                contentToSign: '',
                contentNotToSign: ''
            };
        },

        // 保存配置
        async saveConfig() {
            if (!this.templateId) {
                this.$message.error('模板ID不能为空');
                return;
            }

            this.loading = true;
            try {
                const ruleConfig = this.buildRuleConfig();
                await saveStampRecommendationRule(this.templateId, ruleConfig);
                this.$message.success('保存成功');
            } catch (error) {
                console.error('保存印章推荐配置失败:', error);
                this.$message.error('保存失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        // 构建规则配置对象
        buildRuleConfig() {
            const stampStrategies = [];
            let order = 1;

            // 基础策略配置
            Object.keys(this.strategyConfig).forEach(key => {
                if (this.strategyConfig[key]) {
                    const strategyType = this.strategyTypeMap[key];
                    if (strategyType) {
                        stampStrategies.push({
                            strategyType,
                            order: order++,
                            strategyConfigParam: null
                        });
                    }
                }
            });

            // 位置调优配置
            const upMove = parseFloat(this.positionAdjust.upAdjust) || 0;
            const leftMove = parseFloat(this.positionAdjust.leftAdjust) || 0;
            if (upMove !== 0 || leftMove !== 0) {
                stampStrategies.push({
                    strategyType: 'SEAL_MOVE_CONFIG',
                    order: order++,
                    strategyConfigParam: {
                        upMove: upMove.toString(),
                        leftMove: leftMove.toString()
                    }
                });
            }

            // 关键字配置 - 添加印章
            if (this.keywordConfig.contentToSign.trim()) {
                stampStrategies.push({
                    strategyType: 'ADD_SEAL_BY_KEYWORD',
                    order: order++,
                    strategyConfigParam: {
                        keyword: this.keywordConfig.contentToSign.trim()
                    }
                });
            }

            // 关键字配置 - 移除印章
            if (this.keywordConfig.contentNotToSign.trim()) {
                stampStrategies.push({
                    strategyType: 'REMOVE_SEAL_BY_KEYWORD',
                    order: order++,
                    strategyConfigParam: {
                        keyword: this.keywordConfig.contentNotToSign.trim()
                    }
                });
            }

            return {
                useStampRecommendation: this.enableAgent,
                stampStrategies
            };
        },

        // 处理开关切换
        async handleSwitchChange(value) {
            if (!this.templateId) {
                this.$message.error('模板ID不能为空');
                this.enableAgent = !value; // 回滚状态
                return;
            }

            this.loading = true;
            try {
                await toggleStampRecommendationSwitch(this.templateId, value);
                this.$message.success(value ? '已开启印章推荐功能' : '已关闭印章推荐功能');
            } catch (error) {
                console.error('切换印章推荐开关失败:', error);
                this.$message.error('操作失败，请稍后重试');
                this.enableAgent = !value; // 回滚状态
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="scss">
.ai-agent {
    padding: 20px;
    
    &__switch {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        span {
            margin-left: 10px;
            font-size: 14px;
        }
    }
    
    &__description {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
        
        p {
            margin: 5px 0;
            font-size: 13px;
            line-height: 1.5;
        }
    }
    
    &__config {
        margin-bottom: 30px;
        
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .config-tip {
            font-size: 13px;
            color: #666;
            margin-bottom: 15px;
        }
    }
    
    .rule-item {
        margin-bottom: 15px;
        
        .rule-desc {
            margin-top: 5px;
            margin-left: 24px;
            font-size: 12px;
            color: #999;
        }
    }
    
    &__adjustment {
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .adjustment-item {
            margin-bottom: 20px;
            
            p {
                margin-bottom: 10px;
            }
            
            .position-adjust, .content-adjust {
                margin-left: 20px;
                margin-bottom: 10px;
            }
            
            .adjust-row {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                
                span {
                    font-size: 13px;
                }
            }
            
            .adjust-input {
                width: 80px;
                margin: 0 10px;
            }
            
            .content-input {
                width: 300px;
                margin: 0 10px;
            }
            
            .adjust-tip {
                font-size: 12px;
                color: #999;
                margin-left: 20px;
            }
        }
    }

    &__actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: center;

        .el-button {
            margin: 0 10px;
        }
    }
}
</style>