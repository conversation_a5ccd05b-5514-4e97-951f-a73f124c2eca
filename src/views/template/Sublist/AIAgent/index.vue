<template>
    <div class="ai-agent">
        <div class="ai-agent__switch">
            <el-switch v-model="enableAgent" active-color="#13ce66"></el-switch>
            <span>拖拽Agent</span>
        </div>
        <div class="ai-agent__description">
            <p>凡是从本地上传的文档，Agent都会自动帮你找到各个签约方的盖章处，以代替人工施动盖章处的繁琐操作</p>
            <p>*单份文档页数不能超过50页</p>
            <p>*配合"模板专用章"、"自动盖"等功能，可以实现非标合同盖章的完全自动化</p>
        </div>

        <div class="ai-agent__config">
            <h3>拖章规则配置</h3>
            <p class="config-tip">勾选需要使用的规则，并调整各个规则的排序，以明确规则冲突时的优先级。</p>
            
            <div class="rule-item">
                <el-checkbox v-model="needSignature">需要骑缝章</el-checkbox>
                <p class="rule-desc">*如果不勾选，则不会自动添加骑缝章盖章处</p>
            </div>
            
            <div class="rule-item">
                <el-checkbox v-model="matchSignature">对等盖章：按已有盖章处匹配签约方的盖章处</el-checkbox>
                <p class="rule-desc">*例如，若合同上已有对方的3个印章（分布在不同页面），系统会自动为我方在相应位置也生成3个对应的盖章位置。 注：本功能仅在单方盖章时生效。</p>
            </div>
            
            <div class="rule-item">
                <el-checkbox v-model="endSignature">文件中若没有明确的盖章处，则盖在文件最后一行的文字上</el-checkbox>
                <p class="rule-desc">*不是传统意义上的合同，没有明确的盖章处指示的文件，如内部单据等需要盖章的文件</p>
            </div>
            
            <div class="rule-item">
                <el-checkbox v-model="placeSignature">印章应放置在合理的区域（如盖章栏附近、合同末页公司信息处等）</el-checkbox>
                <p class="rule-desc">*可能会与对等盖章、每页盖章等规则有冲突，需要调整优先级，以确保优先需要满足的规则</p>
            </div>
            
            <div class="rule-item">
                <el-checkbox v-model="everyPageSignature">每页盖章</el-checkbox>
                <p class="rule-desc">*如果是对账单、招投标文件，则每页相同位置都需要盖章</p>
            </div>
        </div>

        <div class="ai-agent__adjustment">
            <h3>自助调优</h3>
            <div class="adjustment-item">
                <p>(1) 盖章位置调优（不含骑缝章）</p>
                <div class="position-adjust">
                    <div class="adjust-row">
                        <span>系统指定的盖章处需向上移动</span>
                        <el-input v-model="upAdjust" size="small" class="adjust-input"></el-input>
                        <span>厘米</span>
                    </div>
                    <div class="adjust-row">
                        <span>系统指定的盖章处需向左移动</span>
                        <el-input v-model="leftAdjust" size="small" class="adjust-input"></el-input>
                        <span>厘米</span>
                    </div>
                </div>
                <p class="adjust-tip">*可填写负数，代表反方向。</p>
                <p class="adjust-tip">*N厘米是打印后的大小，默认情况下一个章的直径约4厘米，可以以此折算</p>
                <p class="adjust-tip">*移动盖章处可能会与已有盖章处重叠，请知悉</p>
            </div>
            
            <div class="adjustment-item">
                <p>(2) 是否盖章调整</p>
                <div class="content-adjust">
                    <div class="adjust-row">
                        <span>若文件中出现</span>
                        <el-input v-model="contentToSign" size="small" class="content-input"></el-input>
                        <span>该内容对应的位置需要盖章</span>
                    </div>
                    <div class="adjust-row">
                        <span>若文件中出现</span>
                        <el-input v-model="contentNotToSign" size="small" class="content-input"></el-input>
                        <span>该内容对应的位置不需要盖章</span>
                    </div>
                </div>
                <p class="adjust-tip">*此处配置后，规则将变成最高优先级</p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'AIAgent',
    props: {
        templateId: {
            type: String,
            required: true
        },
        templateName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            enableAgent: false,
            needSignature: false,
            matchSignature: true,
            endSignature: true,
            placeSignature: true,
            everyPageSignature: false,
            upAdjust: '0',
            leftAdjust: '0',
            contentToSign: '',
            contentNotToSign: ''
        };
    },
    methods: {
        // 可以添加保存配置的方法等
        saveConfig() {
            // 保存配置到后端
        }
    }
};
</script>

<style lang="scss">
.ai-agent {
    padding: 20px;
    
    &__switch {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        span {
            margin-left: 10px;
            font-size: 14px;
        }
    }
    
    &__description {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
        
        p {
            margin: 5px 0;
            font-size: 13px;
            line-height: 1.5;
        }
    }
    
    &__config {
        margin-bottom: 30px;
        
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .config-tip {
            font-size: 13px;
            color: #666;
            margin-bottom: 15px;
        }
    }
    
    .rule-item {
        margin-bottom: 15px;
        
        .rule-desc {
            margin-top: 5px;
            margin-left: 24px;
            font-size: 12px;
            color: #999;
        }
    }
    
    &__adjustment {
        h3 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .adjustment-item {
            margin-bottom: 20px;
            
            p {
                margin-bottom: 10px;
            }
            
            .position-adjust, .content-adjust {
                margin-left: 20px;
                margin-bottom: 10px;
            }
            
            .adjust-row {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                
                span {
                    font-size: 13px;
                }
            }
            
            .adjust-input {
                width: 80px;
                margin: 0 10px;
            }
            
            .content-input {
                width: 300px;
                margin: 0 10px;
            }
            
            .adjust-tip {
                font-size: 12px;
                color: #999;
                margin-left: 20px;
            }
        }
    }
}
</style>